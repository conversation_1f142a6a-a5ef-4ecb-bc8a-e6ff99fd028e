.talent-detail-wrapper {
    :global {
        .ant-layout-content {
            padding: 16px;
            background-color: var(--bg-color);
        }

        transition: all 0.5s;
    }

    .talent-detail-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        gap: 16px;

        .name {
            font-size: 18px;
            font-weight: 600;
            line-height: 18px;
        }

        :global {
            .ant-badge {
                margin: 0 4px;
            }
            .ant-rate {
                font-size: 16px;
            }
        }

        .talent-detail-content__left__wrapper {
            position: relative;
            background-color: #fff;
            width: 68%;
            height: 100%;
            border-radius: 12px;

            .talent-detail-content__left {
                width: 100%;
                height: 100%;
                padding: 20px;
                overflow: auto;

                .talent-detail-content__basic-info__user {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;

                    .talent-detail-content__basic-info__user__left {
                        display: flex;
                        align-items: stretch;
                        gap: 12px;

                        .user-info {
                            padding: 4px 0;
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                            color: var(--main-text-color);
                        }
                    }

                    .talent-detail-content__basic-info__user__right {
                        background-color: var(--bg-color);
                        border-radius: 50px;
                        padding: 4px 8px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        cursor: pointer;

                        .label {
                            color: var(--sub-text-color);
                        }
                    }
                }

                .talent-detail-content__basic-info__tags {
                    display: flex;
                    align-items: center;
                    margin: 8px 0;
                    width: "98%";
                    .tags-source {
                        background-color: #f2790026;
                        color: #dc6e00ff;
                        margin-right: 0;
                    }
                }

                .talent-detail-content__basic-info__AIReason {
                    padding: 8px;
                    border-radius: 4px;
                    background: linear-gradient(90deg, #9d64ff1a 0%, #ffffff1a 64.9%);
                    color: var(--main-text-color);
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .prefix-icon {
                        width: 100px;
                    }

                    .ai-content {
                        width: calc(100% - 100px);
                    }
                }
            }

            .pagination-btn {
                position: absolute;
                top: 50%;
                background-color: rgba(0, 0, 0, 0.5);
                color: #fff;
                border-radius: 50%;
                font-size: 12px;
                opacity: 0.4;
                transition: all 0.4s;
                width: 24px;
                height: 24px;
                cursor: pointer;
                display: flex;
                justify-content: center;
                align-items: center;

                &:hover {
                    opacity: 1;
                    width: 28px;
                    height: 28px;
                    font-size: 16px;
                }
            }

            .left {
                left: -12px;
                &:hover {
                    left: -14px;
                }
            }
            .right {
                right: -12px;
                &:hover {
                    right: -14px;
                }
            }
        }

        .talent-detail-content__right {
            width: 32%;
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: 16px;

            .talent-detail-content__right__top {
                background-color: #fff;
                width: 100%;
                height: 15%;
                border-radius: 12px;
                padding: 20px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            .talent-detail-content__right__bottom {
                background-color: #fff;
                width: 100%;
                height: 85%;
                border-radius: 12px;
                padding: 20px;
                overflow: auto;

                .talent-detail-content__right__bottom__collapse {
                    margin-top: 20px;
                }
            }
        }
    }
}

.AI-label {
    background: linear-gradient(90.61deg, #0099f2ff -2.07%, #843dffff 67.42%, #33d1ccff 111.94%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: "Alibaba PuHuiTi";
    font-weight: 600;
    font-size: 14px;
    margin-right: 8px;
}

.job-collapse-wrapper {
    background-color: var(--bg-color);
    padding: 12px 8px;
    border-radius: 12px;
    margin-bottom: 12px;

    .job-collapse-wrapper__header {
        padding: 0 8px;
        cursor: pointer;
    }

    .job-collapse-wrapper__content {
        background-color: #fff;
        padding: 12px;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.25s linear;

        &.expanded {
            max-height: 1000px; // 设置一个足够大的值
            opacity: 1;
        }

        &.collapsed {
            max-height: 0;
            opacity: 0;
            padding-top: 0;
            padding-bottom: 0;
        }

        .interview-item {
            margin-top: 16px;
            border-bottom: 1px solid var(--border-color);

            &:first-of-type {
                margin-top: 12px;
            }
        }

        .interview-label {
            font-weight: 600;
            font-size: 14px;
            color: var(--main-text-color);
        }

        .interview-item-remark {
            margin-top: 8px;
            margin-bottom: 16px;
            color: #000000;
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
        }
    }
}

.resume-item {
    .resume-item__header {
        font-weight: 600;
        padding-bottom: 4px;
        border-bottom: 1px solid var(--border-color);
    }

    .resume-item__content {
        padding: 16px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;

        .resume-item__content-item-label {
            color: var(--sub-text-color);
            margin-right: 12px;
            text-align: right;
            width: 120px;
        }
        .resume-item__content-item-value {
            color: var(--main-text-color);
        }
    }
}

.interview-info-wrapper {
    .interview-info-title {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 12px;
    }
}

.operation-logs-wrapper {
    :global {
        .ant-timeline-item {
            .ant-timeline-item-label {
                width: 25%;
                font-weight: 600;
                font-size: 14px;
            }
            .ant-timeline-item-tail {
                inset-inline-start: 25%;
                margin-inline-start: 12px;
            }
            .ant-timeline-item-head {
                inset-inline-start: 25%;
                margin-inline-start: 8px;
            }
            .ant-timeline-item-content {
                inset-inline-start: 25% !important;
            }
        }
    }
    .timeline-content {
        .icons {
            transition: all 0.3s;
            margin-right: 12px;
            &.collapsed {
                transform: rotate(-90deg);
            }
        }
        .operation-title {
            font-weight: 600;
            font-size: 14px;
        }
        .reason {
            overflow: hidden;
            max-height: 0;
            opacity: 0;
            transition: all 0.3s;
            padding: 0;
            margin-left: 26px;
            color: var(--sub-text-color);

            &.expanded {
                max-height: 200px;
                opacity: 1;
                padding: 4px 0;
            }
        }
    }
}

.copy-icon {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        color: #1890ff;
        transform: scale(1.1);
    }
}

.ai-question-wrapper {
    user-select: text;
    display: flex;
    flex-direction: column;
    gap: 20px;
    .question-card {
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid var(--border-color);
        .question-header {
            font-size: 16px;
            font-weight: 600;
        }
        .question-content {
            margin-top: 6px;
            color: var(--sub-text-color);
            text-indent: 1em;
        }
    }
}
