.resizable-container {
    position: relative;
    display: flex;
}

.resizer {
    position: absolute;
    z-index: 10;
    cursor: ew-resize;
    transition: all ease 0.3s;

    &:hover,
    &:active {
        background-color: #0099f2;
    }
}

.top,
.bottom {
    height: 2px;
    left: 0;
    right: 0;
    cursor: ns-resize;
}

.left,
.right {
    width: 2px;
    top: 0;
    bottom: 0;
    cursor: ew-resize;
}

.top {
    top: -1px;
}

.bottom {
    bottom: -1px;
}

.left {
    left: -1px;
}

.right {
    right: -1px;
}