.btn-group {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    background-color: #f5f5f5;
}

.continuous-container {
    overflow-y: auto;
    width: 100%;
    max-height: 80vh;
    padding: 20px 0;

    canvas {
        display: block;
        margin: 0 auto 20px auto;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .page-label {
        text-align: center;
        margin: 10px 0;
        color: #666;
        font-size: 14px;
        font-weight: 500;
    }
}
